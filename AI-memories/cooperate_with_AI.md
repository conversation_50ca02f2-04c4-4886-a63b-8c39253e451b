## Как работать с LLM? (BACKUP - оригинальная версия)
1. Накидываешь примерный функционал, что хочешь видеть. Разделяя по мини-версиям и блокам фич.
2. Берешь задачу
3. Накидываешь тезисно видение. Как видишь, что важно, что непонятно
4. Обсуждаешь с LLM концепцию, уточняешь до тех пор пока не понравится и не будет ясности. После удовлетворения просишь создать MD файл в специальной папке с порядковым номером.
    - Главная задача
    - Лучше сфокусироваться, чтобы никто не отвлекал
    - Лучше утром, когда голова свежая
    - Общайся как с другом, рассказывай как видишь, задавай вопросы
5. Просишь дать идеи по финальной версии, улучшить (отсортированные по КПД)
    - “Ты видишь мой проект. Где узкие места? Что можно улучшить?”
6. Прсоишь проанализировать сущесвующий проект, подумать как имплементирвоать фичу в систему
7. Просишь создать подробный план внедрения пошаговый, с подшагами и описанием задач (лучше в отдельном MD файле)
8. Просишь реализовать план. Уточняя чтобы делал и не останавливаясь и останавливался только если что-то непонятно (промт во вложении).
    `Когда ты делаешь сложные задачи в несколкьо этапов с подзадачами - то Делай, не останавливайся если все ок просто переходи к слеюущей. Если есть трудности, то сначала пробуй искать решение сам. Если решение очевидное и то принимай его. Прерывай и спрашивай меня только если что-то непонятно как лучше. `
    - и можешь уйти делать какие-то дела
    - не забудь сказать чтобы он отмечал статусы задач
9. Тестирование
10. Исправление багов

---

## Как работать с LLM? (УЛУЧШЕННАЯ ВЕРСИЯ)

### 🎯 Фаза 1: Планирование и концепция
1. **Накидай общее видение**
   - Примерный функционал, разделенный по мини-версиям
   - Блоки фич с приоритетами
   - Что критично, что можно отложить

2. **Выбери конкретную задачу**
   - Одна задача за раз
   - Четкие границы функционала

3. **Сформулируй видение**
   - Как видишь реализацию
   - Что важно, что непонятно
   - Какие есть сомнения

4. **Обсуди концепцию с LLM**
   - Уточняй до полной ясности
   - Общайся как с другом
   - Задавай вопросы, рассказывай как видишь
   - **Условия для качества:**
     - Сфокусируйся (никто не отвлекает)
     - Лучше утром (свежая голова)
     - Не торопись на этом этапе
   - После удовлетворения → создать MD файл с концепцией

### 🔍 Фаза 2: Глубокий анализ (КРИТИЧНО!)
5. **Попроси идеи по улучшению**
   - "Ты видишь мой проект. Где узкие места? Что можно улучшить?"
   - Отсортированные по КПД предложения

6. **ОБЯЗАТЕЛЬНО: Анализ существующего кода**
   ```
   Промпт: "Изучи существующий код через codebase-retrieval:
   - Как сейчас работают похожие компоненты?
   - Какие паттерны уже используются?
   - Где могут быть конфликты с новой фичей?
   - Покажи примеры интеграции похожих компонентов"
   ```

7. **Создай детальный план**
   - Пошаговый план с подшагами
   - Описание каждой задачи
   - **НОВОЕ:** Проверочные точки после каждого блока
   - Сохранить в отдельном MD файле

8. **Проверка готовности к реализации**
   ```
   Чек-лист:
   - ✅ Все зависимости понятны?
   - ✅ Есть примеры похожего кода?
   - ✅ Понятна архитектура интеграции?
   - ✅ Определены точки проверки?
   ```

### 🚀 Фаза 3: Реализация с контролем качества
9. **Реализация плана (инкрементально)**
   ```
   Промпт: "Реализуй план инкрементально:
   - Делай, не останавливайся если все ОК
   - После каждого значимого блока: ./build.sh и проверка
   - Если что-то ломается - сразу останавливайся и фиксируй
   - Отмечай статусы задач
   - Прерывай только если что-то непонятно как лучше"
   ```
   - Можешь уйти делать дела, но проверяй прогресс

10. **Промежуточные проверки** (после каждого крупного блока)
    - Сборка: `./build.sh`
    - Быстрый тест основного функционала
    - Проверка интеграции с существующими компонентами

### 🧪 Фаза 4: Тестирование и финализация
11. **Тестирование (КРИТИЧНО для uProd!)**
    - **Сначала тесты, потом код** (где возможно)
    - Реальные тесты, не моки
    - **ОБЯЗАТЕЛЬНО:** Тестировать тесты поломкой функционала
    - Проверить интеграцию с существующими компонентами
    - Обновить TESTING.md

12. **Исправление багов**
    - Приоритет: критичные баги
    - Каждый фикс → новый тест
    - Проверка регрессий

### 📋 Полезные промпты для качества:

**Для анализа:**
> "Изучи существующий код через codebase-retrieval. Найди похожие компоненты. Покажи как они интегрированы. Где могут быть подводные камни?"

**Для планирования:**
> "Создай план с проверочными точками. После каждого блока - что должно работать и как это проверить?"

**Для реализации:**
> "Делай инкрементально. После каждого значимого изменения - сборка и проверка. Если что-то ломается - сразу останавливайся и фиксируй."

**Для тестирования:**
> "Создай реальные тесты (не моки). Обязательно протестируй каждый тест поломкой функционала - если тест не ловит поломку, он бесполезен."

### 🎯 Ключ к успеху "сразу работает":
1. **Больше времени на анализ** = меньше багов при реализации
2. **Инкрементальность** = раннее обнаружение проблем
3. **Реальные тесты** = защита от регрессий
4. **Знание кодовой базы** = правильная интеграция